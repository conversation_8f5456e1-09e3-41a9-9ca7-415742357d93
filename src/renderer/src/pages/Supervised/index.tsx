import { Input, message } from 'antd'
import styles from './style/index.module.scss'
import FsAvatar from '@/component/FsAvatar'
import FsButton from '@/component/FsButton'
import useTable from '@/hooks/useTable'
import { useEffect, useState } from 'react'
import { getSupervisedList } from '@/api/supervised'
import { ActivityStatus, getListPropsValue } from '@/enum/status'
import useSendMSg from '@/hooks/useSendMsg'
import { useNavigate } from 'react-router-dom'
import eventBus from '@renderer/utils/eventBus'
import FsTableSelf from '@renderer/component/FsTableSelf'
import { getCurrentTimeZone } from '@renderer/utils/util'
export default function Supervised() {
  const navigate = useNavigate()
  const [search, setSearch] = useState('')
  const { sendMsg } = useSendMSg()
  useEffect(() => {
    eventBus.on('receiveMsg', (currentMsg: MessageItemProps) => {
      if (currentMsg.messageType === 60) {
        if (currentMsg.code === 0) {
          navigate('/Chats?tab=supervised&superviseId=' + currentMsg.groupId)
          console.log('监督成功')
        } else {
          message.error('Supervision of customer service failed')
        }
      }
    })
    return () => {
      eventBus.off('receiveMsg')
    }
  }, [])
  const getTable = async (params: SupervisedListType) => {
    let resData: any = null
    try {
      resData = await getSupervisedList(params)
    } catch (error: any) {
      console.log(error)
      throw new Error(error)
    }
    return resData
  }
  const { tableData, pageInfo, setParamsFunc, setPageFunc, total, loading } =
    useTable({
      pagination: { current: 1, size: 10 },
      params: { search: '', timeZone: getCurrentTimeZone() },
      getTable,
      filterName: { data: 'records' },
      isPolling: true
    })
  const columns: any = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      render(_t: any, r: any) {
        return (
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <>
              <FsAvatar
                name={r.name ? r.name : `ID${r.customersId}`}
                backgroundColor={r.colour}
                style={{
                  width: 28,
                  height: 28,
                  fontSize: 10,
                  marginRight: 4,
                  flexShrink: 0
                }}
              />
              <span>{r.name ? r.name : `ID${r.customersId}`}</span>
            </>
          </div>
        )
      }
    },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
      render(t: any) {
        if (t) {
          return <span>{t}</span>
        } else {
          return <span>-</span>
        }
      }
    },
    {
      title: 'Actions',
      dataIndex: 'Actions',
      key: 'Actions',
      render(_t: any, record: any) {
        return (
          <FsButton
            type="primary"
            style={{ borderRadius: 2 }}
            disabled={!record.actions}
            onClick={() => handleSuperVisedChat(record.groupIdStr)}
          >
            Supervise Chat
          </FsButton>
        )
      }
    },
    {
      title: 'Activity',
      dataIndex: 'activity',
      key: 'activity',
      render(_t: string, record: any) {
        return (
          <div
            className={styles.activity_wrapper}
            style={{ color: getListPropsValue(ActivityStatus, _t)?.color }}
          >
            <i
              className={[
                'iconfont',
                getListPropsValue(ActivityStatus, _t)?.icon
              ].join(' ')}
              style={{ color: getListPropsValue(ActivityStatus, _t)?.color }}
            ></i>
            <span>{getListPropsValue(ActivityStatus, _t)?.label}</span>
          </div>
        )
      }
    },
    {
      title: 'Customer Service',
      dataIndex: 'customerServiceName',
      key: 'customerServiceName',
      render(t: any) {
        if (t) {
          return <span>{t}</span>
        } else {
          return <span>-</span>
        }
      }
    },
    {
      title: 'Country',
      dataIndex: 'isoCode',
      key: 'isoCode'
    },
    {
      title: 'OS/Device',
      dataIndex: '',
      key: '',
      render(_t: any, record: any) {
        return (
          <div>
            <span>{record.os}</span>
            <span>/{record.browser}</span>
          </div>
        )
      }
    },
    {
      title: 'came from',
      dataIndex: 'fromPage',
      key: 'fromPage',
      render: (text: any, record: any) => (
        <div style={{ wordBreak: 'break-all', maxWidth: '300px' }}>{text}</div>
      )
    },
    {
      title: 'Time',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      align: 'right'
    }
  ]

  const handleSuperVisedChat = (id: number) => {
    sendMsg(
      { groupId: id, customerServiceType: 2, action: 1 },
      { messageType: 59 }
    )
  }
  // const pagingChange = (page: number, pageSize?: number) => {
  //   setPageFunc({ current: page, pageSize: pageSize || 10 })
  // }
  const searchPressEnter = (e: any) => {
    setParamsFunc({ search: e.target.value })
  }
  const searchIcon = () => {
    return <i className="iconfont icon-searchsousuo"></i>
  }
  return (
    <div className={styles.supervised__container}>
      <div className={styles.search_wrapper}>
        <Input
          className={styles.input_search}
          placeholder="Search"
          suffix={searchIcon()}
          onPressEnter={searchPressEnter}
          value={search}
          onChange={(e) => setSearch(e.target.value)}
        />
      </div>
      <div className={styles.list_wrapper}>
        <FsTableSelf
          dataSource={tableData}
          columns={columns}
          pagination={false}
          loading={loading}
          rowKey="groupId"
        />
      </div>
      {/* 底部分页 */}
      {/* <div className={styles.pagination_wrapper}>
        <Pagination
          total={total}
          pageSizeOptions={['10', '20', '50']}
          showSizeChanger
          showTotal={(total) => `Total ${total} items`}
          defaultPageSize={10}
          defaultCurrent={1}
          current={pageInfo.current}
          pageSize={pageInfo.size}
          onChange={pagingChange}
        />
      </div> */}
    </div>
  )
}
