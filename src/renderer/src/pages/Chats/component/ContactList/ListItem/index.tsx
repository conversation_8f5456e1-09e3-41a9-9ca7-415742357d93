import useChatInfo from '@/hooks/useChatInfo'
import styles from './index.module.scss'
import FsAvatarProps from '@/component/FsAvatar'
import { useEffect, useState, useRef, useCallback } from 'react'
import draftIcon from '@/assets/image/draftBox.svg'
import { useDispatch, useSelector } from 'react-redux'
import { RootState } from '@renderer/store'
import { updateUserFormData } from '@renderer/store/modules/message'
import { ChatTimer } from '@/utils/chatTimer'
import eventBus from '@/utils/eventBus'
interface ListProps {
  itemData: MessageItemProps
}
//联系人列表项
export default function ListItem(props: ListProps) {
  const dispatch = useDispatch()
  const { itemData } = props
  const [active, setActive] = useState(false)
  const [chatDuration, setChatDuration] = useState('')
  const { chatRoomInfo, setChatInfo, resetMessageCountMap, setChatStatus } =
    useChatInfo()
  const draftBox = useSelector((state: RootState) => state.chat.draftBox)
  const timerRef = useRef<ChatTimer | null>(null)
  const clickChatRoom = () => {
    if (itemData.groupId === chatRoomInfo.groupId) return
    itemData?.isFinished
      ? setChatStatus(itemData.isFinished)
      : setChatStatus(false)
    setChatInfo({ groupId: itemData.groupId })
    resetMessageCountMap(itemData.groupId)
    dispatch(updateUserFormData({}))
  }
  // 初始化计时器
  const initChatTimer = useCallback(() => {
    if (itemData.effectiveTime) {
      // 销毁之前的计时器
      timerRef.current?.destroy()

      // 创建新的计时器
      timerRef.current = new ChatTimer({
        effectiveTime: itemData.effectiveTime,
        onUpdate: setChatDuration
      })

      // 开始计时
      timerRef.current.start()
    } else {
      setChatDuration('')
    }
  }, [itemData.effectiveTime])

  // 处理消息接收，重新开始计时
  const handleMessageReceive = useCallback((msg: MessageItemProps) => {
    console.log('消息处理----------：', msg);
    // 只处理当前聊天室的消息
    if (msg.groupId !== itemData.groupId) return

    // 客户消息 (messageType === 32) 或自己发送的消息 (messageType === 2 或 21)
    if (msg.messageType === 32 || msg.messageType === 2 || msg.messageType === 21) {
      // 更新 effectiveTime 为当前时间并重新开始计时
      console.log('更新时间--------------：');
      const newEffectiveTime = Date.now()
      if (timerRef.current) {
        timerRef.current.updateEffectiveTime(newEffectiveTime)
      } else {
        // 如果计时器不存在，创建新的计时器
        timerRef.current = new ChatTimer({
          effectiveTime: newEffectiveTime,
          onUpdate: setChatDuration
        })
        timerRef.current.start()
      }
    }
  }, [itemData.groupId])

  useEffect(() => {
    itemData.groupId === chatRoomInfo.groupId
      ? setActive(true)
      : setActive(false)
  }, [chatRoomInfo.groupId])

  // 初始化计时器
  useEffect(() => {
    initChatTimer()

    // 清理函数
    return () => {
      timerRef.current?.destroy()
    }
  }, [initChatTimer])

  // 监听消息接收事件
  useEffect(() => {
    eventBus.on('receiveMsg', handleMessageReceive)

    return () => {
      eventBus.off('receiveMsg')
    }
  }, [handleMessageReceive])
  return (
    <div className={styles.list_item_wrapper} onClick={clickChatRoom}>
      <div
        className={`${styles.list_item_content} ${active ? styles.active : ''}`}
      >
        <FsAvatarProps
          style={{ marginRight: '8px' }}
          name={itemData.name}
          onlineStatus={itemData.onlineStatus}
          messageCount={itemData.messageCount}
          backgroundColor={itemData.avatar}
        ></FsAvatarProps>
        <div className={styles.list_item_info}>
          <div className={styles.list_item_name_time}>
            <div className={styles.list_item_name}>{itemData.name}</div>
            <div className={styles.list_item_time_wrapper}>
              <div className={styles.list_item_time}>{itemData.messageTime}</div>
              {chatDuration && (
                <div className={styles.list_item_duration}>{chatDuration}</div>
              )}
            </div>
          </div>
          <div className={styles.list_item_message}>
            {draftBox[itemData.groupId] &&
              chatRoomInfo.groupId !== itemData.groupId && (
                <img className={styles.draft_icon} src={draftIcon}></img>
              )}
            {itemData.msg}
          </div>
        </div>
      </div>
    </div>
  )
}
