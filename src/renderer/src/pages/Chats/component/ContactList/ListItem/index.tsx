import useChatInfo from '@/hooks/useChatInfo'
import styles from './index.module.scss'
import FsAvatarProps from '@/component/FsAvatar'
import { useEffect, useState } from 'react'
import draftIcon from '@/assets/image/draftBox.svg'
import { useDispatch, useSelector } from 'react-redux'
import { RootState } from '@renderer/store'
import { updateUserFormData } from '@renderer/store/modules/message'
interface ListProps {
  itemData: MessageItemProps
}
//联系人列表项
export default function ListItem(props: ListProps) {
  const dispatch = useDispatch()
  const { itemData } = props
  const [active, setActive] = useState(false)
  const { chatRoomInfo, setChatInfo, resetMessageCountMap, setChatStatus } =
    useChatInfo()
  const draftBox = useSelector((state: RootState) => state.chat.draftBox)
  const clickChatRoom = () => {
    if (itemData.groupId === chatRoomInfo.groupId) return
    itemData?.isFinished
      ? setChatStatus(itemData.isFinished)
      : setChatStatus(false)
    setChatInfo({ groupId: itemData.groupId })
    resetMessageCountMap(itemData.groupId)
    dispatch(updateUserFormData({}))
  }
  useEffect(() => {
    itemData.groupId === chatRoomInfo.groupId
      ? setActive(true)
      : setActive(false)
  }, [chatRoomInfo.groupId])
  return (
    <div className={styles.list_item_wrapper} onClick={clickChatRoom}>
      <div
        className={`${styles.list_item_content} ${active ? styles.active : ''}`}
      >
        <FsAvatarProps
          style={{ marginRight: '8px' }}
          name={itemData.name}
          onlineStatus={itemData.onlineStatus}
          messageCount={itemData.messageCount}
          backgroundColor={itemData.avatar}
        ></FsAvatarProps>
        <div className={styles.list_item_info}>
          <div className={styles.list_item_name_time}>
            <div className={styles.list_item_name}>{itemData.name}</div>
            <div className={styles.list_item_time}>{itemData.messageTime}</div>
          </div>
          <div className={styles.list_item_message}>
            {draftBox[itemData.groupId] &&
              chatRoomInfo.groupId !== itemData.groupId && (
                <img className={styles.draft_icon} src={draftIcon}></img>
              )}
            {itemData.msg}
          </div>
        </div>
      </div>
    </div>
  )
}
