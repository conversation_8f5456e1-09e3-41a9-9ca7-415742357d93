import React, {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  FocusEvent<PERSON><PERSON><PERSON>,
  useEffect,
  useImperativeHandle,
  useRef,
  useState
} from 'react'
import styles from './index.module.scss'
import { Popover, Switch, message } from 'antd'
import { uploadFile } from '@/api/common'
import { useDispatch, useSelector } from 'react-redux'
import { RootState } from '@/store'
import { formatLocalTime, generateUUID } from '@/utils/util'
import useSendMSg from '@/hooks/useSendMsg'
import FsEmoji from '@/component/FsEmoji'
import ShortcutMenu from './ShortcutMenu/index'
import { debounce } from 'lodash'
import { getShortcutPromptList } from '@renderer/api/chat'
import useChatInfo from '@renderer/hooks/useChatInfo'
import {
  changeDraftMessage,
  setApplyRetentionStatus,
  updateChatDetail
} from '@renderer/store/modules/chat'
import eventBus from '@renderer/utils/eventBus'
import { useClickAway } from 'ahooks'
import gptLogo from '@/assets/image/gpt_logo.svg'
// import { filterSensitiveWords } from '@renderer/utils/sensitiveWord'

type FooterBoxProps = {
  onMessageSend: (msg: MessageItemProps) => void
  onFileProgress?: (msg: MessageItemProps) => void
  onFileFinished?: (msg: MessageItemProps) => void
  footerRef?: React.MutableRefObject<unknown>
}
export const acceptFile = [
  '.pdf',
  '.doc',
  '.docx',
  '.xls',
  '.xlsx',
  '.jpeg',
  '.jpg',
  '.png',
  '.heic',
  '.heif',
  '.txt',
  '.mp4',
  '.mov',
  '.avi'
]
const acceptImg = ['jpeg', 'jpg', 'png', 'heic', 'heif']
const acceptDoc = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'txt']
const acceptVideo = ['mp4', 'mov', 'avi']

export default function FooterBox(props: FooterBoxProps) {
  const { onMessageSend, onFileProgress, onFileFinished, footerRef } = props
  useImperativeHandle(footerRef, () => ({
    upLoadFiles
  }))
  const dispatch = useDispatch()
  const [textareaValue, setTextareaValue] = useState<string>('')
  const [isPrivate, setIsPrivate] = useState<boolean>(false)
  const [showShortcut, setShowShortcut] = useState<boolean>(false)
  const [promptList, setPromptList] = useState<Array<ShortcutItem>>([])
  const [shortcutLoading, setShortcutLoading] = useState<boolean>(false)
  const [showEmoji, setShowEmoji] = useState<boolean>(false)
  const [previewVisible, setPreviewVisible] = useState<boolean>(false)
  const previewMsgTextMap = useRef({})
  const [disabledType, setDisabledType] = useState<boolean>(false)
  const { sendMsg } = useSendMSg()
  const chatInfo = useSelector((state: RootState) => state.chat.chatRoomInfo)
  const currentMsg: MessageItemProps = useSelector(
    (state: RootState) => state.message.currentReceiveMsg
  )
  const isFinishedChat = useSelector(
    (state: RootState) => state.chat.isFinished
  )
  const chatDetail = useSelector((state: RootState) => state.chat.chatDetail)
  const supervisionList = useSelector(
    (state: RootState) => state.chat.supervisionList
  )
  const customerInfo = useSelector(
    (state: RootState) => state.login.customerInfo
  )
  const chatMode = useSelector((state: RootState) => state.chat.chatMode)
  const applyRetentionStatus = useSelector(
    (state: RootState) => state.chat.applyStatus
  )
  const { setSelectedShortcut } = useChatInfo()
  const inputRef = useRef(null)
  const textareaRef = useRef(null)
  const emojiRef = useRef(null)
  const defaultMsgBody = useRef<MessageItemProps>({
    messageType: 2,
    avatar: customerInfo.avatar,
    name: customerInfo.name,
    sendStatus: 0,
    messageReadStatus: false,
    customerServiceType: chatMode === 'chatting' ? 1 : 2
  })

  const prevDataRef = useRef('')
  prevDataRef.current = textareaValue
  const draftBox = useSelector((state: RootState) => state.chat.draftBox)

  const typingStatus = useRef(false)
  useEffect(() => {
    if (currentMsg.messageType === 66) {
      setPreviewVisible(currentMsg.msg ? true : false)
      previewMsgTextMap.current = {
        ...previewMsgTextMap.current,
        [currentMsg.groupId]: currentMsg.msg
      }
      // setPreviewMsgTextMap(prev => {
      //   console.log(prev)
      //   console.log({[currentMsg.groupId]: currentMsg.msg})
      //   return {...prev , [currentMsg.groupId]: currentMsg.msg}
      // })
      dispatch(updateChatDetail({ looking: currentMsg.url }))
    }
    if (currentMsg.messageType === 37 && chatMode === 'supervised') {
      setDisabledType(true)
    }
    return () => {
      setPreviewVisible(false)
      // setPreviewMsgTextMap({})
      setDisabledType(false)
    }
  }, [currentMsg])

  const messageChange: ChangeEventHandler<HTMLTextAreaElement> = (e) => {
    const target = e.target as HTMLTextAreaElement
    if (target.value.length > 0 && !typingStatus.current) {
      typingStatus.current = true
      handleFocus()
    }
    setTextareaValue(target.value)
  }
  const privateChange = (checked: boolean) => {
    setIsPrivate(checked)
  }
  useEffect(() => {
    supervisionList.length === 0 && setIsPrivate(false)
  }, [supervisionList])
  // 生成消息体
  const generateMsgBody = () => {
    const msgParams = {
      msg: textareaValue,
      groupId: chatInfo.groupId,
      clientMessageId: generateUUID(),
      isPrivate,
      isOfflineMessage: isFinishedChat
    }
    const msgBody: MessageItemProps = {
      messageTime: formatLocalTime(),
      ...defaultMsgBody.current
    }
    if (isPrivate) {
      msgBody.customerServiceType = 2
    }
    return {
      msgParams,
      msgBody
    }
  }
  //发送消息
  const send = () => {
    if (!textareaValue) return
    const { msgParams, msgBody } = generateMsgBody()
    // 匹配留资表单
    if (matchApplyRetention(textareaValue)) {
      msgParams.msg = msgParams.msg.replace('[Automatic]', '')
      //延后发送留资表单
      setTimeout(() => {
        sendMsg(
          {
            groupId: chatInfo.groupId,
            formType: chatDetail.isoCode === 'CN' ? 1 : 2
          },
          { messageType: 63 }
        )
      })
      dispatch(setApplyRetentionStatus(false))
    }

    setTextareaValue('')
    onMessageSend({ ...msgBody, ...msgParams })
    sendMsg(msgParams, { messageType: 2 })
  }
  //回车键发送消息
  const handlePressEnter = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (!e.shiftKey && e.code === 'Enter' && !e.nativeEvent.isComposing) {
      e.preventDefault()
      e.stopPropagation()
      send()
    }
  }
  useEffect(() => {
    // 切换完成之后，填充草稿箱内容
    const currentDraft = draftBox[chatInfo.groupId]
    if (currentDraft) {
      setTextareaValue(currentDraft)
    }
    return () => {
      //将切换之前的草稿存入
      if (chatInfo.groupId !== '0') {
        dispatch(
          changeDraftMessage({
            id: chatInfo.groupId,
            draftMessage: prevDataRef.current
          })
        )
      }
      setTextareaValue('')
    }
  }, [chatInfo.groupId])
  useEffect(() => {
    if (applyRetentionStatus) {
      const applyRetentionMark =
        chatDetail.isoCode === 'CN'
          ? '[Automatic]为了更好地服务于您，请留下您的联系信息，以便我们将来能与您联系。'
          : '[Automatic]Could you please enter your email address so we can log this chat in case it requires further follow up'
      setTextareaValue(applyRetentionMark)
      textareaRef.current.focus()
      dispatch(setApplyRetentionStatus(false))
    }
  }, [applyRetentionStatus])
  useEffect(() => {
    matchShortcut(textareaValue)
    adjustHeight()
  }, [textareaValue])

  //调整输入框高度
  const adjustHeight = () => {
    const target = textareaRef.current as HTMLTextAreaElement
    target.style.height = 'auto'
    target.style.height = target.scrollHeight + 'px'
  }

  // 匹配传入的字符串中是否有'[Automatic]'字符串
  const matchApplyRetention = (text: string) => {
    const reg = /\[Automatic\]/g
    return reg.test(text)
  }

  //点击快捷语提示列表
  const onShortcutClick = (data: ShortcutItem) => {
    if (data?.content) {
      setTextareaValue(data.content)
      textareaRef.current.focus()
    }
    setShowShortcut(false)
  }
  //点击唤起快捷语
  const wakeUpShortcut = () => {
    if (showShortcut) return
    setTextareaValue('#')
    textareaRef.current.focus()
  }

  // 匹配#号快捷语
  const matchShortcut = (text: string) => {
    const reg = /^#+/
    if (reg.test(text)) {
      setShowShortcut(true)
      setShortcutLoading(true)
      fetchShortcuts(text)
    } else {
      setShowShortcut(false)
    }
  }
  const fetchShortcuts = debounce(async (text: string) => {
    const prompt = text.substring(1)
    console.log(prompt)
    const data: any = await getShortcutPromptList({ search: prompt })
    setPromptList(data)
    setShortcutLoading(false)
  }, 500)

  /**
   * @description 点击其他区域关闭表情选择
   */
  useClickAway(() => {
    setShowEmoji(false)
  }, [emojiRef, () => document.querySelector('.emoji_popover')])
  //表情选择
  const emojiClick = (emoji: string) => {
    insertAtCursor(emoji)
    setShowEmoji(false)
  }
  //光标插入文本
  const insertAtCursor = (myValue: string) => {
    const myField = textareaRef.current
    if (myField.selectionStart || myField.selectionStart === 0) {
      const startPos = myField.selectionStart
      const endPos = myField.selectionEnd
      const restoreTop = myField.scrollTop
      myField.value =
        myField.value.substring(0, startPos) +
        myValue +
        myField.value.substring(endPos, myField.value.length)
      if (restoreTop > 0) {
        // restore previous scrollTop
        myField.scrollTop = restoreTop
      }
      myField.focus()
      myField.selectionStart = startPos + myValue.length
      myField.selectionEnd = startPos + myValue.length
    } else {
      myField.value += myValue
      myField.focus()
    }
    setTextareaValue(myField.value)
  }

  /**
   * @description: 输入框聚焦
   * @return {*}
   */
  const handleFocus = () => {
    if (chatMode === 'supervised' || isPrivate) return
    sendMsg({ hasTyping: true, groupId: chatInfo.groupId }, { messageType: 80 })
  }
  /**
   * @description:  输入框失焦
   * @return {*}
   */
  const handleBlur = () => {
    typingStatus.current = false
    if (chatMode === 'supervised' || isPrivate) return
    sendMsg(
      { hasTyping: false, groupId: chatInfo.groupId },
      { messageType: 80 }
    )
  }
  /**
   *
   * @description 粘贴图片或者文档
   */

  const handlePaste = (event: React.ClipboardEvent<HTMLTextAreaElement>) => {
    event.preventDefault()
    const items = (event.clipboardData || window.clipboardData).items
    const text = event.clipboardData.getData('text')
    //判断是文本，粘贴文本
    if (text) {
      insertAtCursor(text)
      return
    }
    //粘贴文件
    if (items && items.length) {
      // 搜索剪切板items
      for (let i = 0; i < items.length; i++) {
        const item = items[i]
        if (item.kind === 'file') {
          const file = item.getAsFile()
          const fileName = file.name.toLowerCase()
          const fileExtension = fileName.substring(
            file.name.lastIndexOf('.') + 1
          )
          if (acceptFile.includes(`.${fileExtension}`)) {
            upLoadFiles(file)
          } else {
            message.error('Unsupported file format.')
          }
        }
      }
    }
  }
  // 文件选择
  const fileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const target = e.target
    const files = target.files

    for (let i = 0; i < files.length; i++) {
      const file = files[i]
      if (validFile(file)) {
        upLoadFiles(file)
      }
    }
    target.value = ''
  }
  //上传文件
  const upLoadFiles = (file: File) => {
    const msgBody: MessageItemProps = {
      ...defaultMsgBody.current,
      messageTime: formatLocalTime(),
      fileType: file.name.substring(
        file.name.lastIndexOf('.') + 1
      ) as FileTypes,
      pre: 0
    }
    // 小于1M的文件用kb表示，大于1M的文件用MB表示
    let fileSize = ''
    if (file.size < 1024 * 1024) {
      fileSize = (file.size / 1024).toFixed(2) + 'KB'
    } else {
      fileSize = (file.size / 1024 / 1024).toFixed(2) + 'MB'
    }
    const msgParams = {
      fileInfo: {
        fileName: file.name,
        fileSize,
        fileUrl: '',
        fileExt: file.name
          .substring(file.name.lastIndexOf('.') + 1)
          .toLowerCase()
      },
      groupId: chatInfo.groupId,
      clientMessageId: generateUUID()
      // isPrivate
    }
    onMessageSend({ ...msgBody, ...msgParams })
    const fd = new FormData()
    fd.append('file', file)
    uploadFile(fd, {
      onUploadProgress: (e: any) => {
        msgBody.pre = Math.floor((e.loaded / e.total) * 100)
        onFileProgress({
          clientMessageId: msgParams.clientMessageId,
          pre: msgBody.pre
        })
        // e.loaded 已经上传的字节数据，e.total 字节数据  转换为1-100的比例值 赋值个pre
      }
    }).then((res: any) => {
      const updateMsg = {
        ...msgParams,
        fileInfo: {
          ...msgParams.fileInfo,
          fileUrl: res.url
        }
      }
      onFileFinished(updateMsg)
      sendMsg(updateMsg, { messageType: 2 })
    })
  }
  //文件校验
  const validFile = (file: File) => {
    const ext = file.name.substring(file.name.lastIndexOf('.') + 1)
    if (acceptImg.includes(ext)) {
      if (file.size > 5 * 1024 * 1024) {
        message.error('Maximum file size 5M.')
        return false
      } else if (file.size === 0) {
        // 禁止空文件上传
        message.error('Please do not upload empty files.')
      }
    }
    if (acceptDoc.includes(ext) || acceptVideo.includes(ext)) {
      if (file.size > 20 * 1024 * 1024) {
        message.error('Maximum file size 20M.')
        return false
      }
    }
    return true
  }

  // 在room切换时，重置输入框内容
  const resetFooter = () => {
    setTextareaValue('')
    setSelectedShortcut('')
  }
  // 设置快捷语
  function setShortcut(content: string) {
    if (content) {
      setTextareaValue(content)
    }
  }
  useEffect(() => {
    eventBus.on('shortcut', (content) => {
      setShortcut(content)
      textareaRef.current.focus()
    })
    return () => {
      eventBus.off('shortcut')
      resetFooter()
    }
  }, [])

  const jumpToGPT = () => {
    window.open(
      'https://www.fs.com/technical_documents.html?country=US&languages=English&currency=USD',
      '_blank'
    )
  }

  return (
    <div className={styles.chat_footer_wrapper}>
      {showShortcut && (
        <ShortcutMenu
          onShortcutClick={onShortcutClick}
          promptList={promptList}
          loading={shortcutLoading}
        ></ShortcutMenu>
      )}
      {previewMsgTextMap.current[chatInfo.groupId] && (
        <div className={styles.previewMsg_wrap}>
          <p>Type: {previewMsgTextMap.current[chatInfo.groupId]}</p>
        </div>
      )}
      <div
        className={`${styles.chat_footer_box} ${
          (chatMode === 'supervised' || isPrivate) && styles.supervised
        }`}
      >
        {disabledType && <div className={styles.disabled_mask}></div>}
        <div className={styles.chat_message_input}>
          {/* <TextArea
            placeholder={
              chatMode === 'supervised' || isPrivate
                ? 'Private messages will be visible to agents only...'
                : 'type your message...'
            }
            bordered={false}
            autoSize={{ minRows: 1, maxRows: 3 }}
            value={textareaValue}
            onChange={messageChange}
            onPressEnter={handlePressEnter}
            ref={textareaRef}
          /> */}
          <textarea
            placeholder={
              chatMode === 'supervised' || isPrivate
                ? 'Private messages will be visible to agents only...'
                : 'type your message...'
            }
            value={textareaValue}
            onPaste={handlePaste}
            onChange={messageChange}
            onKeyDown={handlePressEnter}
            ref={textareaRef}
            // onFocus={handleFocus}
            onBlur={handleBlur}
          />
        </div>
        {/* 聊天工具 */}
        <div className={styles.chat_tools}>
          <div className={styles.chat_tools_left}>
            {chatMode === 'chatting' && (
              <>
                <div className={styles.chat_tools_left_private}>
                  <Switch
                    checked={isPrivate}
                    onChange={privateChange}
                    disabled={supervisionList.length === 0}
                  />
                  <span className={styles['chat_tools_title']}>Private</span>
                </div>
                <div className={styles.chat_tools_line}></div>
              </>
            )}
            <div className={styles.chat_tools_left_emoji} ref={emojiRef}>
              <Popover
                overlayClassName="emoji_popover"
                open={showEmoji}
                trigger="click"
                content={() => <FsEmoji emojiClick={emojiClick} />}
              >
                <div
                  className={styles.chat_tools_emoji}
                  onClick={() => setShowEmoji(true)}
                >
                  <i className="iconfont icon-biaoqing-moren"></i>
                </div>
              </Popover>
            </div>
            <div className={`${styles.chat_tools_sign} shortcut_btn`}>
              <div
                className={styles.chat_tools_sign_icon}
                onClick={wakeUpShortcut}
              >
                <i className="iconfont icon-fuhao-moren"></i>
              </div>
            </div>
            <div className={styles.chat_tools_file}>
              <div className={styles.chat_tools_file_icon}>
                <i className="iconfont icon-wenjian-moren1"></i>
                <input
                  ref={inputRef}
                  type="file"
                  className={styles.chat_tools_file_input}
                  accept={acceptFile.join(',')}
                  onChange={fileChange}
                />
              </div>
            </div>
            <div className={styles.chat_tools_gpt} onClick={jumpToGPT}>
              <img src={gptLogo} alt="gpt_logo" />
            </div>
          </div>
          <div className={styles.chat_tools_right} onClick={send}>
            <i
              className={`iconfont icon-send-moren ${
                textareaValue.length > 0 ? styles.send_icon_active : ''
              }`}
            ></i>
          </div>
        </div>
      </div>
    </div>
  )
}
