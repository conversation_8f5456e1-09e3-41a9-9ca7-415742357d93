import React, {
  createElement,
  useEffect,
  useMemo,
  useRef,
  useState
} from 'react'
import ChatView from '@/component/ChatView'
import styles from './index.module.scss'
import HeaderBox, { CustomerListOption } from './HeaderBox'
import FooterBox, { acceptFile } from './FooterBox'
import useChatInfo from '@/hooks/useChatInfo'
import { getChatDetail, getTransferList } from '@/api/chat'
import { formatUTC, formatUTCToFull } from '@/utils/util'
import { useDispatch, useSelector } from 'react-redux'
import { updateUserFormData } from '@/store/modules/message'
import Empty from '@/component/Empty'
import { RootState } from '@/store'
import useSendMSg from '@/hooks/useSendMsg'
import { updateChatDetail } from '@renderer/store/modules/chat'
import eventBus from '@renderer/utils/eventBus'
import DragBox from './DragBox'
import { message } from 'antd'

export default function ChatMessage() {
  const { sendMsg } = useSendMSg()
  const [messageData, setMessageData] = useState<MessageItemProps[]>([])
  const [historyMessageList, setHistoryMessageList] = useState<
    MessageItemProps[]
  >([])
  const [customerList, setCustomerList] = useState<CustomerListOption[]>([
    {
      userId: 0,
      name: 'All'
    }
  ])
  const historyPagination = useRef({
    page: 1,
    size: 10
  })
  const totalHistory = useRef(1)
  const { chatRoomInfo, setChatDetail, setSupervisionList, chatMode } =
    useChatInfo()
  const customerInfo = useSelector(
    (state: RootState) => state.login.customerInfo
  )
  const isFinished = useSelector((state: RootState) => state.chat.isFinished)
  const currentMsg = useSelector(
    (state: RootState) => state.message.currentReceiveMsg
  ) // 当前接收到的消息
  const dispatch = useDispatch()
  const chatEleRef = useRef<HTMLDivElement>(null) //获取聊天室dom
  const childRef = useRef<any>(null) //获取子组件方法
  const footerRef = useRef(null) //获取底部组件方法
  const prevScrollHeight = useRef(0)
  const [globalLoading, setGlobalLoading] = useState(false)
  const [historyLoading, setHistoryLoading] = useState(false)
  const [dragBoxStyle, setDragBoxStyle] = useState({} as React.CSSProperties)
  // 获取聊天记录
  const fetchChatDetail = async () => {
    const res: any = await getChatDetail({
      groupIdStr: chatRoomInfo.groupId,
      current: historyPagination.current.page,
      size: historyPagination.current.size,
      appId: 3,
      timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      isSupervision: chatMode === 'chatting' ? false : true,
      isFinished: isFinished,
      supervisionCustomerServiceId:
        chatMode === 'chatting' ? '' : `${customerInfo.userId}`
    })
    setHistoryLoading(false)
    setGlobalLoading(false)
    if (res.messageList.records.length > 0) {
      totalHistory.current = res.messageList.pages
      historyPagination.current.page = res.messageList.current
      if (historyPagination.current.page === 1) {
        setChatDetail(res.groupUserChatDetail)
        setSupervisionList(res.supervisionList)
        setHistoryMessageList([...res.messageList.records])
      } else {
        setHistoryMessageList((prev) => [...res.messageList.records, ...prev])
      }
      // dispatch(setHistoryMessage(res.messageList.records))
    }
  }
  const onDropMenuClick = (open) => {
    open && fetchTransferList()
  }
  const fetchTransferList = async () => {
    const data: any = await getTransferList()
    const formatData: CustomerListOption[] = [
      {
        userId: 0,
        name: 'All'
      },
      ...data
    ]
    setCustomerList(formatData)
  }
  const loadMoreHistory = (el: React.MutableRefObject<HTMLDivElement>) => {
    const { scrollTop, scrollHeight } = el.current
    if (
      scrollTop <= 20 &&
      historyPagination.current.page < totalHistory.current
    ) {
      console.log('loadMoreHistory', scrollTop)
      setHistoryLoading(true)
      historyPagination.current.page++
      prevScrollHeight.current = scrollHeight
      fetchChatDetail()
    }
  }
  const formatMessageList = useMemo(() => {
    const formatHistoryList = historyMessageList.map((item) => {
      if (item.messageType === 21 && item.messageVisibilityType === 1) {
        return {
          ...item,
          sendStatus: 1
        }
      } else {
        return item
      }
    })
    const concatList = [...formatHistoryList, ...messageData]
    const formatData = concatList.map((item) => {
      return {
        ...item,
        messageTime: formatUTC(item.messageTime),
        fullTime: formatUTCToFull(item.messageTime)
      }
    })

    return formatData
  }, [messageData, historyMessageList])

  //重置聊天室状态
  const resetChatRoom = () => {
    historyPagination.current.page = 1
    historyPagination.current.size = 10
    setHistoryMessageList([])
    setMessageData([])
    setChatDetail({})
  }

  //请求聊天记录
  useEffect(() => {
    if (chatRoomInfo.groupId !== '0') {
      setGlobalLoading(true)
      fetchChatDetail()
    }
    return () => {
      resetChatRoom()
    }
  }, [chatRoomInfo.groupId])

  // 接收到消息
  useEffect(() => {
    if (currentMsg.groupId === chatRoomInfo.groupId) {
      sendReadMsgRequest(currentMsg)
      handleAckMessage(currentMsg)
      changeReadStatus(currentMsg)
      // 1.消息可见类型为1或者客服类型为2的消息才显示
      if (
        [1].includes(currentMsg.messageVisibilityType) ||
        currentMsg.customerServiceType === 2
      ) {
        setMessageData((prev) => [...prev, currentMsg])
      }
      // 表单更新发布消息
      if (currentMsg.messageType === 51) {
        let updateData = {}
        if (currentMsg.customerName) {
          updateData = {
            ...updateData,
            customerName: currentMsg.customerName
          }
        }
        if (currentMsg.email) {
          updateData = {
            ...updateData,
            email: currentMsg.email
          }
        }
        if (currentMsg.mobile) {
          updateData = {
            ...updateData,
            mobile: currentMsg.mobile
          }
        }
        if (currentMsg.customerName) {
          dispatch(updateUserFormData(currentMsg))
        }

        dispatch(updateChatDetail(updateData))
      }
    }
  }, [currentMsg])
  //在历史消息更新后保持滚动或滚动到底部
  useEffect(() => {
    // 滚动到底部
    if (childRef.current) {
      if (historyPagination.current.page !== 1) {
        const chatViewEl = childRef.current?.chatViewRef.current
        chatViewEl.scrollTop =
          chatViewEl.scrollHeight - prevScrollHeight.current
      } else {
        childRef.current.imgLoad()
        childRef.current.scrollToBottom()
      }
    }
    // 批量处理历史消息中未读消息
    batchHandleUnreadMsg(historyMessageList)
  }, [historyMessageList.length])

  // 实时消息滚动到底部
  useEffect(() => {
    if (childRef.current) {
      childRef.current.scrollToBottom()
    }
  }, [messageData.length])
  // 客服发送的消息加入到消息列表
  const onMessageSend = (msg: MessageItemProps) => {
    setMessageData((prev) => [...prev, msg])
    eventBus.emit('receiveMsg', msg)
  }
  // 监听文件上传进度
  const onFileProgress = (msg: MessageItemProps) => {
    setMessageData((prev) => {
      const index = prev.findIndex(
        (item) => item.clientMessageId === msg.clientMessageId
      )
      if (index !== -1) {
        const updateData = [...prev]
        updateData[index] = {
          ...updateData[index],
          pre: msg.pre
        }
        // prev[index].pre = msg.pre
        return [...updateData]
      }
      return prev
    })
  }
  const onFileFinished = (msg: MessageItemProps) => {
    setMessageData((prev) => {
      const index = prev.findIndex(
        (item) => item.clientMessageId === msg.clientMessageId
      )
      if (index !== -1) {
        const updateData = [...prev]
        updateData[index] = {
          ...updateData[index],
          ...msg
        }
        return [...updateData]
      }
      return prev
    })
    childRef.current.imgLoad('update')
  }
  //批量处理未读消息

  const batchHandleUnreadMsg = (msgList: MessageItemProps[]) => {
    const unreadMsgList = msgList.filter(
      (item) =>
        [32, 21].includes(item.messageType) && item.messageReadStatus === false
    )
    if (unreadMsgList.length > 0) {
      unreadMsgList.forEach((msg) => {
        sendMsg(
          {
            type: 4,
            messageId: msg.messageId,
            clientMessageId: msg.clientMessageId,
            groupId: msg.groupId,
            fromUserId: msg.fromUserId
          },
          { messageType: 34 }
        )
      })
    }
  }
  // 处理ack消息
  const handleAckMessage = (msg: MessageItemProps) => {
    if (msg.messageType === 53) {
      // ack消息签收
      const prevMsgIndex = messageData.findIndex(
        (item) => item.clientMessageId === msg.ackMessageId
      )
      if (prevMsgIndex !== -1) {
        const updateData = [...messageData]
        updateData[prevMsgIndex] = {
          ...updateData[prevMsgIndex],
          sendStatus: 1 //签收成功
        }
        setMessageData([...updateData])
        return
      }
    }
  }
  // 发送消息已读请求
  const sendReadMsgRequest = (msg: MessageItemProps) => {
    if (msg.messageType === 32 || msg.customerServiceType === 2) {
      sendMsg(
        {
          type: 4,
          messageId: msg.messageId,
          clientMessageId: msg.clientMessageId,
          groupId: msg.groupId,
          fromUserId: msg.fromUserId
        },
        { messageType: 34 }
      )
    }
  }
  // 改变消息状态
  const changeReadStatus = (msg: MessageItemProps) => {
    if (msg.messageType === 52) {
      // 消息已读
      const prevMsgIndex = messageData.findIndex(
        (item) => item.clientMessageId === msg.clientMessageId
      )
      console.log('------未读消息index：', prevMsgIndex)
      if (prevMsgIndex !== -1) {
        // const updateData = [...messageData]
        // updateData[prevMsgIndex] = {
        //   ...updateData[prevMsgIndex],
        //   messageReadStatus: true //消息状态改为已读
        // }
        // console.log(updateData)
        setMessageData((prev) => {
          const updateData = [...prev]
          updateData[prevMsgIndex] = {
            ...updateData[prevMsgIndex],
            messageReadStatus: true //消息状态改为已读
          }
          return [...updateData]
        })
        return
      }
    }
  }
  // 处理重发消息
  const resendMessage = (msg: MessageItemProps) => {
    if (msg.retryCount >= 3) {
      markMessageAsFailed(msg)
      return
    }
    msg.retryCount += 1
  }
  // 标记为发送失败
  const markMessageAsFailed = (msg: MessageItemProps) => {}

  /**
   * @description 拖拽上传文件功能
   *
   */
  let lastLeaveEle = null
  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    // 判断一个节点是否是给定节点的后代
    const chatRoomEl = childRef.current?.chatViewRef.current as HTMLDivElement
    if (!chatEleRef.current?.contains(chatRoomEl)) return
    e.preventDefault()
    e.stopPropagation()
  }
  const handleDragEnter = (e: React.DragEvent<HTMLDivElement>) => {
    const chatRoomEl = childRef.current?.chatViewRef.current as HTMLDivElement
    if (!chatEleRef.current?.contains(chatRoomEl)) return
    lastLeaveEle = e.target
    e.stopPropagation()
    e.preventDefault()
    if (Object.keys(dragBoxStyle).length <= 1) {
      setDragBoxStyle({
        display: 'block',
        top: '82px',
        height: chatRoomEl.offsetHeight + 'px'
      })
    }
  }
  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    const chatRoomEl = childRef.current?.chatViewRef.current as HTMLDivElement
    if (!chatEleRef.current?.contains(chatRoomEl)) return
    if (lastLeaveEle === e.target) {
      console.log('handleDragLeave')
      setDragBoxStyle({ display: 'none' })
    }
  }
  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    const chatRoomEl = childRef.current?.chatViewRef.current as HTMLDivElement
    if (!chatEleRef.current?.contains(chatRoomEl)) return
    setDragBoxStyle({ display: 'none' })
    const file = e.dataTransfer.files[0]
    const fileName = file?.name.toLowerCase() || 'unknown'
    const fileExtension = fileName.substring(fileName.lastIndexOf('.') + 1)
    if (acceptFile.includes(`.${fileExtension}`)) {
      footerRef.current.upLoadFiles(file)
    } else {
      message.error('Unsupported file format.')
    }
    e.preventDefault()
  }
  /**
   * @description 更新ticketBtn状态
   */
  useEffect(() => {
    eventBus.on('updateTicketBtn', (data) => {
      const btnIndex = historyMessageList.findIndex(
        (item) => item.ticketId == data.ticketId
      )
      setHistoryMessageList((prev) => {
        const updateData = [...prev]
        updateData[btnIndex] = {
          ...updateData[btnIndex],
          msg: JSON.stringify(data)
        }
        return [...updateData]
      })
    })
    return () => {
      eventBus.off('updateTicketBtn')
    }
  }, [historyMessageList])
  return (
    <div
      className={styles.chat_message_wrapper}
      ref={chatEleRef}
      onDragEnter={handleDragEnter}
      onDragLeave={handleDragLeave}
      onDragOver={handleDragOver}
      onDrop={handleDrop}
    >
      {formatMessageList?.length > 0 ? (
        <>
          <ChatView
            headerBox={createElement(HeaderBox, {
              customerList,
              onDropMenuClick
            })}
            footerBox={createElement(FooterBox, {
              onMessageSend,
              onFileProgress,
              onFileFinished,
              footerRef
            })}
            globalLoading={globalLoading}
            historyLoading={historyLoading}
            messageData={formatMessageList}
            loadMore={loadMoreHistory}
            chatWrapRef={childRef}
          >
            <DragBox style={dragBoxStyle} />
          </ChatView>
        </>
      ) : (
        <div className={styles.empty_wrap}>
          <Empty
            type="chatEmpty"
            style={{ width: '170px', height: '170px' }}
          ></Empty>
          <p>No ongoing chats</p>
        </div>
      )}
    </div>
  )
}
